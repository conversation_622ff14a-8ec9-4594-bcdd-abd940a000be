.lay {
    height: 100%;
    display: flex;
    flex-direction: column;
}
.search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 10px 12px;
    gap: 6px;
    .action {
        display: flex;
        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 5px;
            .filter {
                width: 13px;
                height: 13px;
            }
            .eye {
                width: 16px;
                height: 11px;
            }
            .document {
                width: 12px;
                height: 14px;
            }
        }
    }
}

.content {
    padding: 10px 12px;
    flex: 1;
    .content-scroll {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    .item {
        background: #ffffff;
        border-radius: 4px;
        padding: 12px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        .row {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            .name {
                color: $regular;
                font-size: 15px;
            }
            .value {
                font-size: 14px;
                .quantity {
                    color: $primary;
                    margin-right: 4px;
                }
            }
        }
    }
}
