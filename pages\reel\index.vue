<template>
    <view class="lay">
        <view class="search">
            <uv-search placeholder="请输入盘具名称" :showAction="false"></uv-search>
            <view class="action">
                <view class="icon">
                    <image class="filter" src="/static/filter.png"></image>
                </view>
                <view class="icon">
                    <image class="eye" src="/static/eye.png"></image>
                </view>
                <view class="icon">
                    <image class="document" src="/static/document.png"></image>
                </view>
            </view>
        </view>
        <view class="content">
            <scroll-view scroll-y class="content-scroll">
                <view class="item" v-for="item in 10" :key="item">
                    <view class="row">
                        <view class="name">全钢盘{{ item }}</view>
                        <view class="value">
                            <text class="quantity">105</text>
                            <text class="unit">kg</text>
                        </view>
                    </view>
                    <view class="row">
                        <view class="name">500/250/250/375</view>
                        <view class="value">
                            <text class="quantity">105</text>
                            <text class="unit">m³</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>
        <view>
            <cx-button @onClick="handleClick()" text="盘具计算"></cx-button>
        </view>
    </view>
</template>

<script setup lang="ts">
const handleClick = () => {
    console.log(11);
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
